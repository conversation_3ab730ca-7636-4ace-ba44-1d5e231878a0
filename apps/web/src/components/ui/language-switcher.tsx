"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useLanguage, Language } from "@/contexts/LanguageContext";
import { Globe } from "lucide-react";
import { cn } from "@/lib/utils";

interface LanguageSwitcherProps {
  className?: string;
  variant?: "default" | "ghost" | "outline";
  size?: "default" | "sm" | "lg" | "icon";
}

export function LanguageSwitcher({ 
  className, 
  variant = "ghost", 
  size = "sm" 
}: LanguageSwitcherProps) {
  const { language, setLanguage, t } = useLanguage();

  const handleLanguageChange = (newLanguage: Language) => {
    setLanguage(newLanguage);
  };

  const getCurrentLanguageLabel = () => {
    return language === "zh" ? "中文" : "English";
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant={variant}
          size={size}
          className={cn(
            "flex items-center gap-2 text-gray-600 hover:text-gray-800 transition-colors",
            className
          )}
        >
          <Globe className="h-4 w-4" />
          <span className="hidden sm:inline">{getCurrentLanguageLabel()}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-32">
        <DropdownMenuItem
          onClick={() => handleLanguageChange("zh")}
          className={cn(
            "flex items-center gap-2 cursor-pointer",
            language === "zh" && "bg-gray-100"
          )}
        >
          <span className="text-lg">🇨🇳</span>
          <span>中文</span>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => handleLanguageChange("en")}
          className={cn(
            "flex items-center gap-2 cursor-pointer",
            language === "en" && "bg-gray-100"
          )}
        >
          <span className="text-lg">🇺🇸</span>
          <span>English</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// 简化版本的语言切换按钮，用于紧凑的空间
export function CompactLanguageSwitcher({ className }: { className?: string }) {
  const { language, setLanguage } = useLanguage();

  const toggleLanguage = () => {
    setLanguage(language === "zh" ? "en" : "zh");
  };

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleLanguage}
      className={cn(
        "flex items-center gap-1 text-gray-600 hover:text-gray-800 transition-colors px-2",
        className
      )}
    >
      <Globe className="h-3 w-3" />
      <span className="text-xs font-medium">
        {language === "zh" ? "EN" : "中"}
      </span>
    </Button>
  );
}
