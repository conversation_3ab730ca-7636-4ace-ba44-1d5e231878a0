"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import { getCookie, setCookie } from "@/lib/cookies";

export type Language = "zh" | "en";

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

const LANGUAGE_COOKIE_KEY = "oc_language";

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguageState] = useState<Language>("zh"); // 默认中文

  useEffect(() => {
    // 从cookie中读取语言设置
    const savedLanguage = getCookie(LANGUAGE_COOKIE_KEY) as Language;
    if (savedLanguage && (savedLanguage === "zh" || savedLanguage === "en")) {
      setLanguageState(savedLanguage);
    }
  }, []);

  const setLanguage = (lang: Language) => {
    setLanguageState(lang);
    setCookie(LANGUAGE_COOKIE_KEY, lang);
  };

  const t = (key: string): string => {
    return getTranslation(key, language);
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error("useLanguage must be used within a LanguageProvider");
  }
  return context;
}

// 翻译映射
const translations: Record<string, Record<Language, string>> = {
  // 模型选择器
  "model.openai": {
    zh: "OpenAI",
    en: "OpenAI",
  },
  "model.glm": {
    zh: "GLM",
    en: "GLM",
  },
  "model.anthropic": {
    zh: "Anthropic",
    en: "Anthropic",
  },
  "model.gemini": {
    zh: "Gemini",
    en: "Gemini",
  },
  "model.fireworks": {
    zh: "Fireworks",
    en: "Fireworks",
  },
  "model.groq": {
    zh: "Groq",
    en: "Groq",
  },
  "model.ollama": {
    zh: "Ollama",
    en: "Ollama",
  },
  "model.azure": {
    zh: "Azure OpenAI",
    en: "Azure OpenAI",
  },

  // 欢迎页面
  "welcome.startBlankCanvas": {
    zh: "从空白画布开始",
    en: "Start with a blank canvas",
  },
  "welcome.newMarkdown": {
    zh: "新建 Markdown",
    en: "New Markdown",
  },
  "welcome.newCodeFile": {
    zh: "新建代码文件",
    en: "New Code File",
  },
  "welcome.orWithMessage": {
    zh: "或者发送消息",
    en: "or with a message",
  },
  "welcome.languages": {
    zh: "编程语言",
    en: "Languages",
  },

  // 工具栏
  "toolbar.addComments": {
    zh: "添加注释",
    en: "Add comments",
  },
  "toolbar.addLogs": {
    zh: "添加日志",
    en: "Add logs",
  },
  "toolbar.portLanguage": {
    zh: "转换语言",
    en: "Port language",
  },
  "toolbar.fixBugs": {
    zh: "修复错误",
    en: "Fix bugs",
  },
  "toolbar.translate": {
    zh: "翻译",
    en: "Translate",
  },

  // 语言切换
  "language.switch": {
    zh: "切换语言",
    en: "Switch Language",
  },
  "language.chinese": {
    zh: "中文",
    en: "Chinese",
  },
  "language.english": {
    zh: "English",
    en: "English",
  },

  // 编程语言
  "lang.php": {
    zh: "PHP",
    en: "PHP",
  },
  "lang.typescript": {
    zh: "TypeScript",
    en: "TypeScript",
  },
  "lang.javascript": {
    zh: "JavaScript",
    en: "JavaScript",
  },
  "lang.cpp": {
    zh: "C++",
    en: "C++",
  },
  "lang.java": {
    zh: "Java",
    en: "Java",
  },
  "lang.python": {
    zh: "Python",
    en: "Python",
  },
  "lang.html": {
    zh: "HTML",
    en: "HTML",
  },
  "lang.sql": {
    zh: "SQL",
    en: "SQL",
  },

  // 错误信息
  "error.languageNotSelected": {
    zh: "未选择语言",
    en: "Language not selected",
  },
  "error.selectLanguageToContinue": {
    zh: "请选择一种语言以继续",
    en: "Please select a language to continue",
  },
  "error.portLanguageError": {
    zh: "转换语言错误",
    en: "Port language error",
  },
  "error.codeAlreadyIn": {
    zh: "代码已经是",
    en: "The code is already in",
  },

  // 模型配置
  "config.temperature": {
    zh: "温度",
    en: "Temperature",
  },
  "config.temperatureDesc": {
    zh: "控制创造性 - 较低值产生更专注的输出，较高值产生更多样化和想象力的输出。",
    en: "Controls creativity - lower for focused outputs, higher for more variety and imagination.",
  },
  "config.maxTokens": {
    zh: "最大令牌数",
    en: "Max tokens",
  },
  "config.maxTokensDesc": {
    zh: "设置AI响应的长度 - 更多令牌意味着更长、更详细的响应。",
    en: "Set how long the AI's response can be - more tokens mean longer, more detailed responses.",
  },
  "config.resetToDefaults": {
    zh: "重置为默认值",
    en: "Reset to Defaults",
  },

  // 翻译选项
  "translate.english": {
    zh: "英语",
    en: "English",
  },
  "translate.mandarin": {
    zh: "中文",
    en: "Mandarin",
  },
  "translate.hindi": {
    zh: "印地语",
    en: "Hindi",
  },
  "translate.spanish": {
    zh: "西班牙语",
    en: "Spanish",
  },
  "translate.french": {
    zh: "法语",
    en: "French",
  },

  // 通用UI文本
  "common.loading": {
    zh: "加载中...",
    en: "Loading...",
  },
  "common.save": {
    zh: "保存",
    en: "Save",
  },
  "common.cancel": {
    zh: "取消",
    en: "Cancel",
  },
  "common.confirm": {
    zh: "确认",
    en: "Confirm",
  },
  "common.delete": {
    zh: "删除",
    en: "Delete",
  },
  "common.edit": {
    zh: "编辑",
    en: "Edit",
  },
  "common.copy": {
    zh: "复制",
    en: "Copy",
  },
  "common.paste": {
    zh: "粘贴",
    en: "Paste",
  },
  "common.search": {
    zh: "搜索",
    en: "Search",
  },
  "common.settings": {
    zh: "设置",
    en: "Settings",
  },
  "common.help": {
    zh: "帮助",
    en: "Help",
  },
  "common.close": {
    zh: "关闭",
    en: "Close",
  },

  // 应用标题和描述
  "app.title": {
    zh: "Open Canvas",
    en: "Open Canvas",
  },
  "app.description": {
    zh: "LangChain 开放画布聊天界面",
    en: "Open Canvas Chat UX by LangChain",
  },

  // 聊天界面
  "chat.placeholder": {
    zh: "输入消息...",
    en: "Type a message...",
  },
  "chat.send": {
    zh: "发送",
    en: "Send",
  },
  "chat.newChat": {
    zh: "新建对话",
    en: "New Chat",
  },
  "chat.clearHistory": {
    zh: "清除历史",
    en: "Clear History",
  },

  // 文件操作
  "file.upload": {
    zh: "上传文件",
    en: "Upload File",
  },
  "file.download": {
    zh: "下载文件",
    en: "Download File",
  },
  "file.export": {
    zh: "导出",
    en: "Export",
  },
  "file.import": {
    zh: "导入",
    en: "Import",
  },
};

function getTranslation(key: string, language: Language): string {
  const translation = translations[key];
  if (!translation) {
    console.warn(`Translation key "${key}" not found`);
    return key;
  }
  return translation[language] || key;
}
